#!/usr/bin/env python3
"""
Verify Neo4j Storage from GraphRAG Pipeline
"""

import os
from neo4j import GraphDatabase
from dotenv import load_dotenv

load_dotenv()

def check_neo4j_storage():
    """Check what was actually stored in Neo4j"""
    
    uri = os.getenv("NEO4J_URI")
    user = os.getenv("NEO4J_USERNAME", "neo4j")
    password = os.getenv("NEO4J_PASSWORD")
    
    driver = GraphDatabase.driver(uri, auth=(user, password))
    
    try:
        with driver.session() as session:
            # Check all nodes
            result = session.run("MATCH (n) RETURN labels(n) as labels, count(n) as count")
            print("=== Nodes in Neo4j ===")
            for record in result:
                print(f"Labels: {record['labels']}, Count: {record['count']}")
            
            # Check all relationships
            result = session.run("MATCH ()-[r]->() RETURN type(r) as rel_type, count(r) as count")
            print("\n=== Relationships in Neo4j ===")
            for record in result:
                print(f"Type: {record['rel_type']}, Count: {record['count']}")
            
            # Check chunk nodes specifically
            result = session.run("MATCH (c:Chunk) RETURN c.text[0..100] as text_preview, c.index as index LIMIT 5")
            print("\n=== Chunk Nodes ===")
            for record in result:
                print(f"Index: {record['index']}, Text: {record['text_preview']}...")
            
            # Check if any legal entities were stored
            legal_labels = ["Case", "Judge", "Court", "Attorney", "Plaintiff", "Defendant", "Damages"]
            for label in legal_labels:
                result = session.run(f"MATCH (n:{label}) RETURN count(n) as count")
                count = result.single()['count']
                if count > 0:
                    print(f"\n=== {label} Nodes: {count} ===")
                    result = session.run(f"MATCH (n:{label}) RETURN n LIMIT 3")
                    for record in result:
                        print(f"  {dict(record['n'])}")
            
            # Check GraphRAG-specific nodes
            result = session.run("MATCH (n:__KGBuilder__) RETURN count(n) as count")
            kg_count = result.single()['count']
            print(f"\n=== GraphRAG Builder Nodes: {kg_count} ===")
            
            if kg_count > 0:
                result = session.run("MATCH (n:__KGBuilder__) RETURN n LIMIT 5")
                for record in result:
                    node = record['n']
                    print(f"  ID: {node.get('__tmp_internal_id')}, Labels: {list(node.labels)}")
    
    finally:
        driver.close()

if __name__ == "__main__":
    check_neo4j_storage()
