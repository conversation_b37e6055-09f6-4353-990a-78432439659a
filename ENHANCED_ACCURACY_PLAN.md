# GraphRAG + LangExtract Enhancement Plan
## Improving Extraction Accuracy with Dual Processing

**Corrected Architecture**: All documents through GraphRAG + LangExtract enhancement for complex cases

---

## Architecture Overview

```
Legal Document from GCS/Supabase
           ↓
    GraphRAG Pipeline (PRIMARY)
    - Vertex AI extraction
    - voyage-context-3 embeddings  
    - Neo4j storage
           ↓
    Document Complexity Analysis
           ↓
    ┌─────────────┬──────────────┐
    │   Simple    │   Complex    │
    │ Documents   │  Documents   │
    │ (70%)       │  (30%)       │
    │             │              │
    │ GraphRAG    │ GraphRAG +   │
    │ Results     │ LangExtract  │
    │ (Final)     │ Enhancement  │
    └─────────────┴──────────────┘
           ↓              ↓
    Neo4j Storage   Entity Fusion
                         ↓
                   Enhanced Results
                         ↓
                   Neo4j Storage
```

## Implementation Plan

### Phase 1: Data Pipeline Integration (Week 1)

#### 1.1 GCS/Supabase Data Reader
```python
class LegalDataReader:
    """Read ingested legal documents from GCS and Supabase"""
    
    def __init__(self):
        self.gcs_client = storage.Client()
        self.supabase = create_client(
            os.getenv("SUPABASE_URL"), 
            os.getenv("SUPABASE_KEY")
        )
    
    async def get_documents_for_processing(self, 
                                         batch_size: int = 50,
                                         practice_area: str = None,
                                         priority_filter: str = None) -> List[Dict]:
        """
        Retrieve documents from Supabase with GCS text content
        Priority: unprocessed -> low_accuracy -> complex_cases
        """
        
    async def get_document_content(self, gcs_path: str) -> str:
        """Download document content from GCS"""
        
    def mark_processing_status(self, doc_id: str, status: str, 
                             graphrag_results: Dict = None,
                             langextract_results: Dict = None):
        """Update processing status in Supabase"""
```

#### 1.2 Document Complexity Classifier
```python
class DocumentComplexityClassifier:
    """Determine if document needs LangExtract enhancement"""
    
    def analyze_complexity(self, document: Dict, 
                         graphrag_results: Dict) -> Dict[str, Any]:
        """
        Analyze document characteristics and GraphRAG results
        to determine if LangExtract enhancement would improve accuracy
        """
        
        complexity_factors = {
            # Citation density
            "citation_count": len(re.findall(r'\d+\s+S\.W\.\d+d\s+\d+', document['text'])),
            
            # Entity confidence from GraphRAG
            "low_confidence_entities": sum(1 for e in graphrag_results['entities'] 
                                         if e.get('confidence', 1.0) < 0.7),
            
            # Document characteristics
            "document_length": len(document['text']),
            "court_complexity": self._assess_court_complexity(document),
            "party_count": self._count_parties(document),
            "damage_complexity": self._assess_damages(document),
            
            # Practice area complexity
            "practice_area_complexity": self._get_practice_area_score(
                document.get('practice_area', 'unknown')
            )
        }
        
        # Calculate complexity score
        complexity_score = self._calculate_complexity_score(complexity_factors)
        
        return {
            "complexity_score": complexity_score,
            "needs_enhancement": complexity_score > 0.6,
            "factors": complexity_factors,
            "enhancement_reason": self._get_enhancement_reason(complexity_factors)
        }
```

#### 1.3 Enhanced Processing Pipeline
```python
class EnhancedLegalPipeline:
    """Main pipeline combining GraphRAG + LangExtract"""
    
    def __init__(self):
        self.graphrag_pipeline = LegalGraphRAGPipeline()
        self.langextract_enhancer = LangExtractEnhancer()
        self.complexity_classifier = DocumentComplexityClassifier()
        self.data_reader = LegalDataReader()
        self.entity_fusion = EntityFusionEngine()
    
    async def process_document(self, document: Dict) -> Dict[str, Any]:
        """
        Process document through GraphRAG + optional LangExtract enhancement
        """
        
        # Step 1: Always run GraphRAG (PRIMARY)
        graphrag_results = await self.graphrag_pipeline.process_legal_document(document)
        
        # Step 2: Analyze complexity
        complexity_analysis = self.complexity_classifier.analyze_complexity(
            document, graphrag_results
        )
        
        # Step 3: Enhance if needed
        if complexity_analysis["needs_enhancement"]:
            langextract_results = await self.langextract_enhancer.extract_entities(document)
            
            # Step 4: Fuse results for improved accuracy
            enhanced_results = await self.entity_fusion.merge_extractions(
                graphrag_results, langextract_results, document
            )
            
            return {
                "document_id": document["id"],
                "processing_type": "enhanced",
                "graphrag_results": graphrag_results,
                "langextract_results": langextract_results,
                "final_results": enhanced_results,
                "complexity_analysis": complexity_analysis,
                "improvement_metrics": self._calculate_improvement_metrics(
                    graphrag_results, enhanced_results
                )
            }
        else:
            return {
                "document_id": document["id"],
                "processing_type": "standard",
                "final_results": graphrag_results,
                "complexity_analysis": complexity_analysis
            }
```

### Phase 2: Entity Fusion Engine (Week 2)

#### 2.1 Smart Entity Fusion
```python
class EntityFusionEngine:
    """Merge GraphRAG and LangExtract results for improved accuracy"""
    
    async def merge_extractions(self, 
                              graphrag_results: Dict,
                              langextract_results: Dict,
                              document: Dict) -> Dict[str, Any]:
        """
        Intelligently merge entity extractions from both systems
        Priority: GraphRAG relationships + LangExtract entity precision
        """
        
        # Keep ALL GraphRAG relationships (critical for graph)
        final_relationships = graphrag_results.get('relationships', [])
        
        # Enhance entities with LangExtract precision
        enhanced_entities = await self._enhance_entities(
            graphrag_entities=graphrag_results.get('entities', []),
            langextract_entities=langextract_results.get('extractions', []),
            document_text=document['text']
        )
        
        # Add any new entities found only by LangExtract
        additional_entities = await self._find_additional_entities(
            graphrag_results, langextract_results
        )
        
        return {
            "entities": enhanced_entities + additional_entities,
            "relationships": final_relationships,
            "fusion_metadata": {
                "graphrag_entity_count": len(graphrag_results.get('entities', [])),
                "langextract_entity_count": len(langextract_results.get('extractions', [])),
                "final_entity_count": len(enhanced_entities + additional_entities),
                "entities_enhanced": len(enhanced_entities),
                "entities_added": len(additional_entities),
                "confidence_improvement": self._calculate_confidence_improvement()
            }
        }
    
    async def _enhance_entities(self, graphrag_entities, langextract_entities, document_text):
        """Enhance GraphRAG entities with LangExtract precision"""
        enhanced = []
        
        for graphrag_entity in graphrag_entities:
            # Find matching LangExtract entity
            langextract_match = self._find_matching_entity(
                graphrag_entity, langextract_entities
            )
            
            if langextract_match:
                # Enhance with LangExtract precision
                enhanced_entity = {
                    **graphrag_entity,
                    "enhanced": True,
                    "langextract_text": langextract_match.extraction_text,
                    "confidence": max(
                        graphrag_entity.get('confidence', 0.0),
                        0.9  # LangExtract high confidence
                    ),
                    "extraction_source": "graphrag+langextract",
                    "position_verified": self._verify_position(
                        langextract_match.extraction_text, document_text
                    )
                }
                enhanced.append(enhanced_entity)
            else:
                # Keep original GraphRAG entity
                enhanced.append({
                    **graphrag_entity,
                    "enhanced": False,
                    "extraction_source": "graphrag_only"
                })
        
        return enhanced
```

#### 2.2 Quality Improvement Metrics
```python
class QualityMetrics:
    """Track improvement from LangExtract enhancement"""
    
    def calculate_improvement(self, original_results, enhanced_results):
        """Calculate quality improvement metrics"""
        
        return {
            "entity_count_improvement": (
                len(enhanced_results['entities']) - 
                len(original_results['entities'])
            ),
            "confidence_improvement": self._calculate_avg_confidence_delta(),
            "precision_improvement": self._calculate_precision_delta(),
            "new_entities_found": self._count_new_entities(),
            "entities_enhanced": self._count_enhanced_entities(),
            "cost_analysis": {
                "additional_cost": self._calculate_enhancement_cost(),
                "value_per_dollar": self._calculate_value_metric()
            }
        }
```

### Phase 3: Scaling with Real Data (Week 3)

#### 3.1 Batch Processing Pipeline
```python
class ScalableBatchProcessor:
    """Process large batches of documents from GCS/Supabase"""
    
    async def process_batch(self, 
                          batch_size: int = 50,
                          practice_areas: List[str] = None,
                          priority_filter: str = "unprocessed") -> Dict[str, Any]:
        """
        Process batches of documents with smart resource management
        """
        
        # Get documents from Supabase
        documents = await self.data_reader.get_documents_for_processing(
            batch_size=batch_size,
            practice_area=practice_areas,
            priority_filter=priority_filter
        )
        
        results = {
            "total_documents": len(documents),
            "processed": 0,
            "enhanced": 0,
            "errors": 0,
            "quality_improvements": [],
            "processing_times": [],
            "cost_analysis": {}
        }
        
        # Process with concurrency control
        semaphore = asyncio.Semaphore(5)  # Max 5 concurrent
        
        async def process_single(document):
            async with semaphore:
                try:
                    result = await self.enhanced_pipeline.process_document(document)
                    
                    # Update Supabase with results
                    await self._update_document_results(document["id"], result)
                    
                    results["processed"] += 1
                    if result["processing_type"] == "enhanced":
                        results["enhanced"] += 1
                        results["quality_improvements"].append(
                            result.get("improvement_metrics", {})
                        )
                    
                    return result
                    
                except Exception as e:
                    results["errors"] += 1
                    logger.error(f"Failed to process {document['id']}: {e}")
                    return {"error": str(e), "document_id": document["id"]}
        
        # Execute batch
        batch_results = await asyncio.gather(
            *[process_single(doc) for doc in documents],
            return_exceptions=True
        )
        
        return results
```

#### 3.2 Integration with Existing Data
```python
class ExistingDataIntegration:
    """Integrate with already ingested GCS/Supabase data"""
    
    def identify_enhancement_candidates(self) -> List[str]:
        """
        Query Supabase to find documents that would benefit from enhancement
        """
        query = """
        SELECT document_id, case_name, practice_area, processing_metadata
        FROM legal_documents 
        WHERE 
            (processing_metadata->>'entity_confidence_avg')::float < 0.7
            OR (processing_metadata->>'citation_count')::int > 5
            OR practice_area IN ('complex_litigation', 'mass_tort', 'class_action')
            OR char_length(plain_text) > 10000
        ORDER BY 
            (processing_metadata->>'entity_confidence_avg')::float ASC,
            (processing_metadata->>'citation_count')::int DESC
        LIMIT 1000
        """
        
    async def reprocess_with_enhancement(self, document_ids: List[str]):
        """Reprocess existing documents with LangExtract enhancement"""
        
    def update_accuracy_metrics(self, document_id: str, 
                              improvement_data: Dict):
        """Update Supabase with improvement metrics"""
```

### Phase 4: Testing and Validation (Week 4)

#### 4.1 Accuracy Validation Framework
```python
class AccuracyValidationFramework:
    """Validate that LangExtract enhancement improves accuracy"""
    
    async def run_accuracy_test(self, test_document_ids: List[str]) -> Dict:
        """
        Test accuracy improvement on known documents
        """
        
        results = {
            "test_documents": len(test_document_ids),
            "accuracy_improvements": [],
            "entity_precision_delta": 0.0,
            "entity_recall_delta": 0.0,
            "relationship_preservation": 1.0,  # Should be 100%
            "cost_benefit_ratio": 0.0
        }
        
        for doc_id in test_document_ids:
            # Get original GraphRAG results
            original = await self._get_original_results(doc_id)
            
            # Run enhanced processing
            enhanced = await self.enhanced_pipeline.process_document_by_id(doc_id)
            
            # Calculate accuracy metrics
            improvement = self._calculate_accuracy_improvement(original, enhanced)
            results["accuracy_improvements"].append(improvement)
        
        return results
```

#### 4.2 Performance Monitoring
```python
class EnhancementMonitoring:
    """Monitor performance and quality of enhancement system"""
    
    def track_metrics(self):
        """Track key performance indicators"""
        return {
            "enhancement_rate": "% of documents enhanced",
            "accuracy_improvement": "Average confidence score increase",
            "entity_discovery": "New entities found per document", 
            "cost_efficiency": "Improvement value per dollar spent",
            "processing_throughput": "Documents per hour",
            "error_rate": "Failed enhancements per batch"
        }
```

## Implementation Roadmap

### Week 1: Data Pipeline Integration
- [ ] Build GCS/Supabase data reader
- [ ] Create document complexity classifier
- [ ] Implement enhanced processing pipeline
- [ ] Test with 10 sample documents

### Week 2: Entity Fusion Engine
- [ ] Build entity fusion algorithms
- [ ] Implement quality improvement metrics
- [ ] Create confidence scoring system
- [ ] Test fusion accuracy on 50 documents

### Week 3: Scaling with Real Data
- [ ] Implement batch processing pipeline
- [ ] Integrate with existing Supabase data
- [ ] Process 500 real documents
- [ ] Measure performance and cost

### Week 4: Testing and Validation
- [ ] Run accuracy validation tests
- [ ] Implement monitoring dashboard
- [ ] Optimize based on results
- [ ] Prepare for production deployment

## Success Metrics

### Primary Goals
1. **Accuracy Improvement**: 15%+ increase in entity precision for complex documents
2. **Relationship Preservation**: 100% of GraphRAG relationships maintained
3. **Coverage Increase**: 10%+ more entities discovered through enhancement
4. **Cost Efficiency**: Enhancement cost < 30% of base processing cost

### Quality Indicators
- Entity confidence score improvement
- Reduced false positive rate
- Better legal citation extraction
- Improved proper noun recognition
- Enhanced monetary amount precision

## Expected Outcomes

- **Enhanced Accuracy**: 15-25% improvement for complex legal documents
- **Complete Graph Structure**: All relationships preserved from GraphRAG
- **Scalable Processing**: Handle existing GCS/Supabase data efficiently
- **Quality Monitoring**: Real-time accuracy and improvement tracking
- **Production Ready**: Robust pipeline for continuous enhancement

This corrected approach ensures ALL documents go through GraphRAG while selectively enhancing complex cases with LangExtract for improved accuracy, working with your existing ingested data in GCS and Supabase.