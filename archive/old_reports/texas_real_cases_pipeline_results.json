{"processing_completed": true, "total_cases_loaded": 111, "duplicates_skipped": 0, "new_cases_processed": 11, "embeddings_generated": 11, "supabase_stored": 11, "pinecone_stored": 11, "neo4j_stored": 11, "processing_time_minutes": 0.02389913002649943, "throughput_cases_per_minute": 4644.520527605936, "batch_results": [{"batch_id": 0, "processed": 25, "new_cases": 25, "supabase_stored": 25, "pinecone_stored": 25, "neo4j_stored": 25, "processing_time": 0.003704071044921875, "success": true}, {"batch_id": 1, "processed": 25, "new_cases": 25, "supabase_stored": 25, "pinecone_stored": 25, "neo4j_stored": 25, "processing_time": 0.0011730194091796875, "success": true}, {"batch_id": 2, "processed": 25, "new_cases": 25, "supabase_stored": 25, "pinecone_stored": 25, "neo4j_stored": 25, "processing_time": 0.006036043167114258, "success": true}, {"batch_id": 3, "processed": 25, "new_cases": 25, "supabase_stored": 25, "pinecone_stored": 25, "neo4j_stored": 25, "processing_time": 0.007261991500854492, "success": true}, {"batch_id": 4, "processed": 11, "new_cases": 11, "supabase_stored": 11, "pinecone_stored": 11, "neo4j_stored": 11, "processing_time": 0.0005681514739990234, "success": true}], "success_rate": 100.0}