# Phase 2: Complete Production Pipeline Plan

## 🎯 Target Production Architecture

```
GCS Raw Documents → Contextual Chunking → Voyage-context-3 Embeddings → Multi-Storage
       ↓                    ↓                      ↓                      ↓
   PDF/HTML/XML      Legal-Aware Chunks      1024-dim Contextual    Pinecone + Supabase + Neo4j
```

## 📋 Complete Production Flow

### Key Insights from Research

**Voyage-context-3 Contextual Approach:**
- Document-level context preservation - Each chunk embedding captures global document context
- Single-pass processing - The entire document is processed together, not chunk-by-chunk
- 32K token context window - Can handle very large legal documents
- Performance boost - 14-23% better retrieval than standard embeddings

**Neo4j GraphRAG SDK Integration:**
- Built-in chunking pipeline with semantic awareness
- Automatic entity extraction from contextualized chunks
- Hierarchical graph clustering for community detection
- Legal domain optimization for entity relationships

## 🏗️ Architecture Components

### Stage 1: Enhanced Document Processing Pipeline

```python
class LegalDocumentProcessor:
    """
    Production-ready processor leveraging voyage-context-3 contextual approach
    """
    
    def __init__(self):
        # Core components optimized for legal documents
        self.gcs_client = EnhancedGCSClient()
        self.legal_chunker = ContextAwareLegalChunker()
        self.voyage_embedder = VoyageContextualEmbedder()
        self.graphrag_pipeline = EnhancedLegalGraphRAG()
        self.storage_orchestrator = MultiStorageOrchestrator()
    
    async def process_full_document_batch(self, gcs_paths: List[str]) -> ProcessingReport:
        """
        Process complete legal documents through contextual pipeline
        """
        results = []
        
        for gcs_path in gcs_paths:
            try:
                # 1. Load full document with metadata
                document = await self.load_legal_document(gcs_path)
                
                # 2. Voyage-context-3 contextual processing
                contextual_chunks = await self.process_with_voyage_context(document)
                
                # 3. GraphRAG entity extraction with context
                graph_results = await self.extract_legal_entities(contextual_chunks)
                
                # 4. Multi-storage with global consistency
                storage_result = await self.store_across_systems(
                    document, contextual_chunks, graph_results
                )
                
                results.append({
                    "status": "success",
                    "document_id": document.id,
                    "chunks_processed": len(contextual_chunks),
                    "entities_extracted": len(graph_results.entities),
                    "storage_status": storage_result
                })
                
            except Exception as e:
                results.append({
                    "status": "error", 
                    "gcs_path": gcs_path,
                    "error": str(e)
                })
        
        return ProcessingReport(results)
```

### Stage 2: Contextual Chunking Strategy

```python
class ContextAwareLegalChunker:
    """
    Legal document chunker optimized for voyage-context-3
    """
    
    def __init__(self):
        # Legal document structure patterns
        self.legal_separators = [
            "\n\n### HOLDING:",     # Court holdings
            "\n\n### DISPOSITION:", # Court dispositions  
            "\n\nHELD:",           # Legal holdings
            "\n\nCONCLUSION:",     # Legal conclusions
            "\n\nWHEREFORE:",      # Prayer for relief
            "\n\nIT IS ORDERED:",  # Court orders
            "\n\n---",             # Section breaks
            "\n\n",                # Paragraph breaks
        ]
        
    async def create_contextual_chunks(self, document: LegalDocument) -> List[ContextualChunk]:
        """
        Create chunks that preserve legal document structure for voyage-context-3
        """
        
        # 1. Preserve document metadata as context
        document_context = {
            "case_name": document.case_name,
            "court": document.court,
            "jurisdiction": document.jurisdiction,
            "practice_area": document.practice_area,
            "date_filed": document.date_filed
        }
        
        # 2. Legal-aware chunking (preserve legal sections)
        chunks = []
        sections = self.split_by_legal_structure(document.text)
        
        for i, section in enumerate(sections):
            # 3. Create overlapping chunks with legal context
            section_chunks = self.chunk_with_overlap(
                text=section,
                chunk_size=2000,  # Optimal for voyage-context-3
                overlap=200,
                preserve_sentences=True
            )
            
            for j, chunk_text in enumerate(section_chunks):
                chunk = ContextualChunk(
                    id=f"{document.id}_s{i}_c{j}",
                    text=chunk_text,
                    document_context=document_context,
                    section_index=i,
                    chunk_index=j,
                    legal_section_type=self.identify_legal_section(chunk_text)
                )
                chunks.append(chunk)
        
        return chunks
```

### Stage 3: Voyage-Context-3 Integration

```python
class VoyageContextualEmbedder:
    """
    Voyage-context-3 embedder optimized for legal documents
    """
    
    def __init__(self):
        self.client = voyageai.Client(api_key=os.getenv("VOYAGE_API_KEY"))
        self.model = "voyage-context-3"
        self.output_dimension = 1024
    
    async def embed_document_with_context(self, chunks: List[ContextualChunk]) -> List[EmbeddedChunk]:
        """
        Use voyage-context-3 contextualized embedding for entire document
        """
        
        # 1. Prepare inputs for contextualized embedding
        # Each inner list represents chunks from same document
        contextualized_inputs = [[chunk.text for chunk in chunks]]
        
        # 2. Generate contextualized embeddings in single pass
        result = self.client.contextualized_embed(
            inputs=contextualized_inputs,
            model=self.model,
            input_type="document",
            output_dimension=self.output_dimension
        )
        
        # 3. Map embeddings back to chunks with context preserved
        embedded_chunks = []
        chunk_embeddings = result.results[0].embeddings
        
        for chunk, embedding in zip(chunks, chunk_embeddings):
            embedded_chunk = EmbeddedChunk(
                id=chunk.id,
                text=chunk.text,
                embedding=embedding,  # Contains global document context
                document_context=chunk.document_context,
                legal_section_type=chunk.legal_section_type,
                embedding_model="voyage-context-3"
            )
            embedded_chunks.append(embedded_chunk)
        
        return embedded_chunks
```

### Stage 4: Enhanced GraphRAG with Context

```python
class EnhancedLegalGraphRAG:
    """
    Legal GraphRAG leveraging voyage-context-3 contextual chunks
    """
    
    def __init__(self):
        # Enhanced pipeline with contextual embeddings
        self.kg_pipeline = SimpleKGPipeline(
            llm=self.setup_vertex_ai_llm(),
            embedder=VoyageContextualEmbedder(),  # Uses voyage-context-3
            driver=self.neo4j_driver,
            text_splitter=None,  # We handle chunking separately
            schema=self.legal_schema,
            perform_entity_resolution=True
        )
    
    async def extract_entities_from_contextual_chunks(self, 
                                                    embedded_chunks: List[EmbeddedChunk]) -> GraphExtractionResult:
        """
        Extract legal entities using contextual chunk information
        """
        
        all_entities = []
        all_relationships = []
        
        for chunk in embedded_chunks:
            # Process each contextual chunk through GraphRAG
            result = await self.kg_pipeline.run_async(
                text=chunk.text,
                # Pass context metadata to enhance extraction
                metadata={
                    "case_name": chunk.document_context["case_name"],
                    "court": chunk.document_context["court"],
                    "practice_area": chunk.document_context["practice_area"],
                    "legal_section": chunk.legal_section_type,
                    "global_uid": chunk.id
                }
            )
            
            # Extract entities with enhanced context
            entities, relationships = await self.extract_with_context_enhancement(
                chunk.id, result
            )
            
            all_entities.extend(entities)
            all_relationships.extend(relationships)
        
        return GraphExtractionResult(
            entities=all_entities,
            relationships=all_relationships,
            contextual_clusters=self.create_legal_clusters(all_entities)
        )
```

### Stage 5: Multi-Storage Orchestration

```python
class MultiStorageOrchestrator:
    """
    Orchestrate storage across Pinecone, Supabase, and Neo4j with global consistency
    """
    
    async def store_complete_document(self, 
                                    document: LegalDocument,
                                    embedded_chunks: List[EmbeddedChunk],
                                    graph_results: GraphExtractionResult) -> StorageResult:
        """
        Store across all systems with transaction-like consistency
        """
        
        global_uid = generate_global_uid(document.id)
        
        try:
            # 1. Pinecone: Contextual embeddings for semantic search
            pinecone_result = await self.store_in_pinecone(
                embedded_chunks=embedded_chunks,
                namespace="texas-legal-contextual",
                global_uid=global_uid
            )
            
            # 2. Supabase: Structured data with processing status
            supabase_result = await self.store_in_supabase(
                document=document,
                chunks=embedded_chunks,
                processing_metadata={
                    "embedding_model": "voyage-context-3",
                    "chunk_count": len(embedded_chunks),
                    "entity_count": len(graph_results.entities),
                    "global_uid": global_uid,
                    "processing_timestamp": datetime.utcnow()
                }
            )
            
            # 3. Neo4j: Knowledge graph (already stored by GraphRAG)
            neo4j_result = await self.validate_neo4j_storage(
                entities=graph_results.entities,
                relationships=graph_results.relationships,
                global_uid=global_uid
            )
            
            return StorageResult(
                status="success",
                pinecone_ids=pinecone_result.ids,
                supabase_case_id=supabase_result.case_id,
                neo4j_node_count=neo4j_result.node_count,
                global_uid=global_uid
            )
            
        except Exception as e:
            # Implement rollback strategy
            await self.rollback_partial_storage(global_uid)
            raise ProcessingError(f"Storage failed for {document.id}: {e}")
```

## 🚀 Implementation Timeline

### Week 1-2: Infrastructure Enhancement
```python
# Tasks:
1. Fix GCS authentication and implement document reader
2. Enhance voyage-context-3 embedder integration  
3. Update GraphRAG pipeline with contextual chunking
4. Implement global UID tracking system
```

### Week 3-4: Pipeline Integration
```python
# Tasks:
1. Build complete document processing pipeline
2. Implement multi-storage orchestrator
3. Add error handling and checkpointing
4. Create processing monitoring and metrics
```

### Week 5-6: Testing & Validation
```python
# Tasks:
1. Test with 100 sample documents
2. Validate cross-system consistency
3. Performance optimization and tuning
4. Quality metrics validation
```

### Week 7-8: Production Deployment
```python
# Tasks:
1. Process full 346K+ document corpus
2. Monitor performance and quality
3. Implement incremental processing
4. Documentation and handoff
```

## 📊 Expected Outcomes

**Pinecone**: ~2M+ contextualized embeddings with 14-23% better retrieval accuracy
**Supabase**: Complete case metadata with processing status and cross-references  
**Neo4j**: Rich legal knowledge graph with enhanced entity relationships
**Global Consistency**: All systems linked via global_uid for seamless querying

This approach leverages voyage-context-3's contextual awareness while maintaining the robust GraphRAG pipeline. The result will be a production-grade legal intelligence platform with superior semantic understanding.

## 🔗 Implementation Reference

This plan serves as the blueprint for implementing the complete production pipeline that processes:
- 346K+ Texas legal documents from GCS
- Contextual chunking optimized for legal structure
- Voyage-context-3 embeddings for enhanced retrieval
- Multi-storage consistency across Pinecone, Supabase, and Neo4j
- Rich knowledge graph extraction via GraphRAG

Date: 2025-08-16
Status: Plan Complete - Ready for Implementation